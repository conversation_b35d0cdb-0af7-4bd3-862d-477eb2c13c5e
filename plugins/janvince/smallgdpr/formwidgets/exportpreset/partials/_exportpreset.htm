<div class="form-group text-field span-full is-required">
    
    <label>
        <?= e(trans('janvince.smallgdpr::lang.formwidgets.exportpreset.file_name')) ?>
    </label>
    
    <input id="<?= $this->alias ?>-file-name" type="text" name="file_name" value="<?= e(trans('janvince.smallgdpr::lang.formwidgets.exportpreset.file_name_default')) ?>" class="form-control" />
    
    <p class="help-block after-field">
        <?= e(trans('janvince.smallgdpr::lang.formwidgets.exportpreset.file_name_comment')) ?>
    </p>

</div>

<div class="form-group span-full">
    
    <button
        class="btn btn-danger oc-icon-download"
        onclick="$(this).data('request-data', {
            path: $('#<?= $this->alias ?>-file-name').val()
            })"
        data-request="onExportPreset"
        data-request-data="path: '<?= e(trans('janvince.smallgdpr::lang.formwidgets.exportpreset.file_name_default')) ?>'"
        data-stripe-load-indicator>
            <?= e(trans('janvince.smallgdpr::lang.settings.form_fields.export')) ?>
        </button>

</div>
