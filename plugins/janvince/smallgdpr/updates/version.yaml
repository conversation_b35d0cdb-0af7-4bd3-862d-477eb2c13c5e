1.0.0:
    - First version of Small GDPR
1.1.0:
    - Added settings in modal window
    - Added Bootrap 3 styling
1.1.1:
    - Updated default presets and README
1.1.2:
    - Fixed sgCookies array to reflect required cookies group state
1.1.3:
    - Fixed scripts
1.1.4:
    - Fixed sgCookies array
1.1.5:
    - Fixed default EN preset
1.1.6:
    - Fixed default active cookies group to be active even without explicit consent
1.1.7:
    - Removed option Default active
1.1.8:
    - Added Static Pages snippet for Cookies manage component
    - Changed trans function usage
    - Changed default CSS style
    - Added custom CSS class for Manage cookies and Cookies bar
1.1.9:
    - Removed unnecessary line in Manage cookies component
1.2.0:
    - Added Default enabled option for cookies groups for cookies to be executed without explicit consent
    - Updated sgCookies array method
    - Updated default presets
1.2.1:
    - Fixed values checks in CookiesSettings
1.2.2:
    - Added option to hide Cookies bar on specific Page or Layout with View Bag property
1.2.3:
    - Updated hideCookiesBar check to support Static Pages
1.2.4:
    - Added optional cookies group script file to be executed
1.2.5:
    - Updated styling, removed unnecessary styles
1.2.6:
    - Added option to run JS only in production mode
1.2.7:
    - Changed fields order in form
1.3.0:
    - Added export settings function
1.3.1:
    - Import file can be now selected from Media panel
    - Updated export and import functions
1.3.2:
    - Fixed import function
1.4.0:
    - Added option do have more scripts and files in scripts group (old scripts data are automatically migrated to the new structure)
    - Added optional title for scripts for internal use
    - v14_migration.php
1.4.1:
    - Extended import tab to be able to use Media, own path or default preset file
1.4.2:
    - Removed forgotten dump
1.4.3:
    - Fixed migration not working on new installs
1.4.4:
    - Fixed folders and models name case
1.4.5:
    - Cookies JS code placed in code window now requires <script> tags to be present! Please check your code.
1.4.6:
    - Updated translation and docs.
1.4.7:
    - Fixed components file name case
1.4.8:
    - Added sgCookies to Cookies Manage component (for case when you do not want to use Cookies Bar in your layout or page)
1.4.9:
    - Fixed CookiesSettings model
1.4.10:
    - Moved modal help-block div from label tag (to be HTML5 valid)
1.4.11:
    - Fixed localization
1.5.0:
    - Added Twig support for Translate plugin (thanks evwerkz)
1.5.1:
    - Changed buttons HTML attributes field to prevent naming collision
1.6.0:
    - Changed settings for import (added second btn for import default settings)
1.7.0:
    - Added option to specify pages to run scripts on
    - Added option to disable specific script from execution (usable for temporary disable script)
    - Added script for Google Analytics in default import files
1.7.1:
    - Fixed HTML attributes field localization in Settings
1.8.0:
    - Added Russian and German language (thanks evwerkz)
    - Added Bootstrap 5 support (thanks evwerkz)
1.8.1:
    - Updated Bootstrap 5 modal support (thanks evwerkz)
1.9.1:
    - Added optional Cookies bar strip CSS styles
1.10.0:
    - Added Slovak translation, fixed Czech translation
1.10.1:
    - Fixed cookie lifetime function
1.10.2:
    - Fixed external URL handling with cookies bar buttons
1.11.2:
    - If you edited default components markup (or you use only parts of it), please check if everything works after update. If you use plugin as is, you should be fine :)
    - Added option to disable page reload after cookies are set (for cookiesBar and cookiesManage components)
    - Updated default components markup (fixed duplicates, updated JS code)
1.12.0:
    - Fixed versions numbering
1.12.1:
    - Fixed modal hide function
1.13.0:
    - Updated default presets with new GA code
1.14.0:
    - Added option to display top strip in Bootstrap container
1.14.1:
    - Fixed css styles for topline strip in container
1.14.2:
    - Updated css styles for topline strip in container
1.15.0:
    - Added Reject all button option, updated import presets
1.15.1:
    - Fixed cookies bar buttons function
1.15.2:
    - Updated css for Bootstrap box UI
1.15.3:
    - Updated presets texts
1.15.4:
    - Fixed bug in JS
1.15.5:
    - Fixed bug in Bootstrap 5 modal form template
1.16.0:
    - Added option to require cookies consent for each web site language separately
1.17.0:
    - Updated default presets with Google Analytics code to run without consent with disabled storage
1.18.0:
    - Added Finnish language (thanks mediaclinic)
    - Updated presets
1.18.1:
    - Fixed version yaml for OC3
1.18.2:
    - Fixed Twig code for OC3
1.19.0:
    - Updated preset
1.19.1:
    - Fixed css
1.20.0:
    - Added option to pre-check cookies list items in modal window
1.21.0:
    - OC3 compatibility
1.22.0:
    - Strict variables fix (thanks @SamBrishes)
1.23.0:
    - Added option to run JS script after Accept-all Button click
1.23.1:
    - Fixed export settings
1.24.0:
    - Replaced jQuery with vanilla JS (thanks @anik1ng)
1.25.0:
    - Simplified Chinese Language (thanks @ZhiweiWu0425)
1.26.0:
    - Updated templates for Google Consent v2
1.26.1:
    - Modal reload logic updated
1.27.0:
    - French translation (thanks Jean-Daniel Kneubühler)
1.27.1:
    - Added btn-disable-all class to cookiesbar-btn-default