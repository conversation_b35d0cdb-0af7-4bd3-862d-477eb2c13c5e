{% set showModal = null %}

{% if cookiesSettingsGet('cookies_bar_add_styles', null) == 3 %}

    <div class="cookies-bar-container">

        <div class="container">

            <div class="row">

                <div class="col-xs-12">

{% endif %}


<div id="cookies-bar" class="cookies-bar {{ cookiesSettingsGet('cookies_bar_css_class') }}">

    {% if cookiesSettingsGet('cookies_bar_title', null) %}

        <h3 class="title">{{ cookiesSettingsGet('cookies_bar_title') }}</h3>

    {% endif %}

    {% if cookiesSettingsGet('cookies_bar_content', null) %}

        <div class="content">{{ cookiesSettingsGet('cookies_bar_content')|raw }}</div>

    {% endif %}

    {% if cookiesSettingsGet('cookies_bar_buttons', null)|length %}

        <div class="buttons">

            {% for button in cookiesSettingsGet('cookies_bar_buttons') %}

                {% if button.show_modal %}

                    {% set showModal = button.show_modal %}

                {% endif %}

                {% partial __SELF__ ~ '::cookiesbar-btn-' ~ cookiesSettingsGet('ui_style', 'default')  button = button %}

            {% endfor %}

        </div>

    {% endif %}

</div>

{% if cookiesSettingsGet('cookies_bar_add_styles', null) == 3 %}

                </div>
            
            </div>
        
        </div>
        
    </div>

{% endif %}


{% if showModal %}

    {% partial __SELF__ ~ '::cookiesbar-modal-' ~ cookiesSettingsGet('ui_style', 'default') %}
    
{% endif %}

{% put scripts %}

    <script>

        document.querySelector('#cookies-bar .btn-accept-all').addEventListener('click', function(e) {
            e.preventDefault();

            {% partial '@set-cookies' %}

            if (document.querySelector('#cookies-bar')) {
                document.querySelector('#cookies-bar').style.display = 'none';
            }

            {% for button in cookiesSettingsGet('cookies_bar_buttons', null)  %}

                {% if button.accept_all_cookies_btn_script %}

                    {{ button.accept_all_cookies_btn_script|raw }}

                {% endif %}

            {% endfor %}

            {% if cookiesSettingsGet('cookies_bar_disable_page_reload', 0) == 0 %}

                location.reload(true);

            {% endif %}

        });

        document.querySelector('#cookies-bar .btn-disable-all').addEventListener('click', function(e) {
            e.preventDefault();

            {% partial '@set-cookies' setOnlyRequiredCookies = true %}

            if(document.querySelector('#cookies-bar')) {
                document.querySelector('#cookies-bar').style.display = 'none';
            }

            {% if cookiesSettingsGet('cookies_bar_disable_page_reload', 0) == 0 %}

                location.reload(true);

            {% endif %}

        });

    </script>

{% endput %}
