<div class="modal fade" id="sg-settings-modal-{{ __SELF__ }}" tabindex="-1" role="dialog" aria-labelledby="sg-settings-modal-{{ __SELF__ }}">

    <div class="modal-dialog modal-lg" role="document">

        <div class="modal-content">

            <div class="modal-header">

                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                
                <h4 class="modal-title">{{cookiesSettingsGet('cookies_manage_title')}}</h4>

            </div>

            <div class="modal-body">

                {% if cookiesSettingsGet('cookies_manage_content', null) %}

                    <div class="content">{{ cookiesSettingsGet('cookies_manage_content')|raw }}</div>

                {% endif %}

                {% partial '@list-cookies' 
                    disablePageReload = cookiesSettingsGet('cookies_bar_disable_page_reload', 0) 
                    disableSaveButton = true 
                    modal = true
                    customId = '-modal' %}

            </div>

            <div class="modal-footer">
                
                <button type="button" 
                        data-dismiss="modal"
                        class="btn btn-default">{{ 'janvince.smallgdpr::lang.settings.form_fields.cancel'|trans }}</button>
                        
                <button type="button" 
                        id="cookies-manage-save-modal" 
                        class="btn btn-primary">{{ 'janvince.smallgdpr::lang.settings.form_fields.save_settings'|trans }}</button>

            </div>

        </div>

    </div>

</div>
