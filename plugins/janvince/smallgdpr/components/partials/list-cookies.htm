{% partial '@list-cookies-' ~ cookiesSettingsGet('ui_style', 'default') disableSaveButton = disableSaveButton|default(false) customId = customId|default('custom') %}

{% put scripts %}

<script>

    $('#cookies-manage-save{{ customId|default('custom') }}').click(function() {

        {% partial '@set-cookies' setOnlyManageCookies = true %}

        {% if cookiesSettingsGet('cookies_manage_disable_page_reload', 0) == 0 %}

            location.reload(true);

        {% else %}

            if( $('#cookies-bar').length )
            {
                $('#cookies-bar').hide();
            }

            if( $('#sg-settings-modal-{{ __SELF__ }}').length )
            {
                $('#sg-settings-modal-{{ __SELF__ }}').modal('hide');
            }

        {% endif %}

    });

</script>

{% endput %}