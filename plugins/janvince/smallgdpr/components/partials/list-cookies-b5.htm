{% if cookiesSettingsGet('cookies', null)|length %}

    <form class="list-cookies">

        {% for cookie in cookiesSettingsGet('cookies') %}

        <div class="form-check">
            <input class="form-check-input {{ cookie.required ? 'disabled' : ''  }}" type="checkbox"
                   id="sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}{{customId}}"
                   {{ (sgCookies[cookie.slug] is defined and sgCookies[cookie.slug]) or (cookie.required) or (modal is defined and modal and cookie.default_modal_checked) ? 'checked' : '' }} {{ cookie.required ? 'disabled' : ''  }}>
            <label class="form-check-label" for="sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}{{customId}}">
                {{ cookie.title }}
            </label>
        </div>

        {% if cookie.description %}
        <div class="description help-block">{{ cookie.description|raw }}</div>
        {% endif %}

        {% endfor %}

        <br>

        {% if disableSaveButton is empty %}

            <button type="button" id="cookies-manage-save" class="btn btn-primary">
                {{ 'janvince.smallgdpr::lang.settings.form_fields.save_settings'|trans }}
            </button>

        {% endif %}

    </form>

{% endif %}
