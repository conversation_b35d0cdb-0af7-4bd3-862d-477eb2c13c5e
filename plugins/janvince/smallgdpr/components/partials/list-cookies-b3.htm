{% if cookiesSettingsGet('cookies', null)|length %}

    <form class="list-cookies">

        {% for cookie in cookiesSettingsGet('cookies') %}

            <div class="checkbox {{ cookie.required ? 'disabled' : ''  }}">
            
                <label>

                    <input type="checkbox" id="sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}{{customId}}" {{ sgCookies[cookie.slug] or (cookie.required) or (modal and cookie.default_modal_checked) ? 'checked' : '' }} {{ cookie.required ? 'disabled' : ''  }}> <strong>{{ cookie.title }}</strong>
                
                </label>
                    
                {% if cookie.description %}
                    <div class="description help-block">{{ cookie.description|raw }}</div>
                {% endif %}

            </div>

        {% endfor %}

        {% if disableSaveButton is empty %}

            <button type="button" 
                    id="cookies-manage-save" 
                    class="btn btn-primary">{{ 'janvince.smallgdpr::lang.settings.form_fields.save_settings'|trans }}</button>

        {% endif %}

    </form>

{% endif %}