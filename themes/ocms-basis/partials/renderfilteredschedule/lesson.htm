{% set excluded = item.excluded_dates|split('|') %}
{% set holiday = false %}
{% set date = date(item.date) %}
{% set holiday_start = date(item.holiday_start) %}
{% set holiday_end = date(item.holiday_end) %}
{% if date >= holiday_start and date <= holiday_end %}
    {% set holiday = true %}
{% endif %}

{% if item.categories %}
    {% set categorieids = [] %}
    {% for cat in item.categories %}
        {% set categorieids = categorieids|merge(['"' ~ cat.id ~ '"']) %}
    {% endfor %}
{% endif %}

{% if item.date not in excluded and not holiday %}
    <div
        class="py-3 px-4 border border-gray-300 bg-white hover:bg-primary-100 hover:border-primary-500 {{ this.theme.design.border_radius }} mb-3"
        x-data="{
            obj: { categories: [{{ categorieids|join(', ') }}] },
        }"
        x-show="
            locations.length === 0 && categories.length === 0 ||
            locations.length > 0 && locations.includes('{{ item.location.id }}') && categories.length === 0 ||
            categories.length > 0 && obj.categories.some(category => categories.includes(category)) && locations.length === 0 ||
            locations.length > 0 && locations.includes('{{ item.location.id }}') && obj.categories.some(category => categories.includes(category))"
        x-cloak>

        <a href="{{ item.course.landingpage | link }}">
            <div class="space-y-1">
                <div class="text-lg font-bold text-gray-800">
                    {{ item.course.title }}
                </div>
                <div class="text-gray-600">
                    {{ item.start | date('H:i') }} - {{ item.end | date('H:i') }}
                </div>
                {# <div class="text-sm text-gray-500">{{ item.instructor.first_name }} {{
                    item.instructor.last_name ?: '' }}
                </div> #}
            </div>
            <div class="space-y-2 py-2">
                {% if item.categories %}
                    {# <div class="flex flex-wrap gap-1">
                        <div class="bg-primary-500 text-white py-0.5 px-2 rounded-full text-xs font-medium">{{ item.category.title }}</div>
                    </div> #}
                    <div class="flex flex-wrap gap-1">
                        {% for category in item.categories %}
                            <div class="bg-primary-500 text-white py-0.5 px-2 rounded-full text-xs font-medium">{{ category.title }}</div>
                        {% endfor %}
                        {# {% for category in item.categories|slice(0,2) %}
                            <div class="bg-primary-500 text-white py-0.5 px-2 rounded-full text-xs font-medium">{{ category.title }}</div>
                        {% endfor %} #}
                        {# {% if item.categories|length > 2 %}
                            {% set categorielist = [] %}
                            {% for cat in item.categories|slice(2) %}
                                {% set categorielist = categorielist|merge([cat.title]) %}
                            {% endfor %}
                            <div class="bg-primary-500 text-white py-0.5 px-2 rounded-full text-xs font-medium" x-data x-tooltip="{{ categorielist|join(', ') }}">+{{ item.categories|length - 2 }}</div>
                        {% endif %} #}
                    </div>
                {% endif %}
                {% if item.tags %}
                    <div class="flex flex-wrap gap-1">
                        {% for tag in item.tags|slice(0,2) %}
                            <div class="bg-primary-200 text-primary-700 py-0.5 px-2 rounded-full text-xs font-medium">{{ tag }}</div>
                        {% endfor %}
                        {% if item.tags|length > 2 %}
                            <div class="bg-primary-200 text-primary-700 py-0.5 px-2 rounded-full text-xs font-medium" x-data x-tooltip="{{ item.tags|slice(2)|join(', ') }}">+{{ item.tags|length - 2 }}</div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <hr class="my-2">
            <div class="flex flex-col justify-center -mx-2">
                <div class="flex flex-wrap justify-between gap-x-3 gap-y-2">
                    {% if item.location %}
                        <div class="text-sm font-semibold text-gray-500 px-2">
                            <span class="w-2"><i class="text-primary-700 fa-solid fa-location-dot pr-1"></i></span>
                            <span>{{ item.location.title }}</span>
                        </div>
                    {% endif %}
                    {% if item.instructor %}
                        <div class="text-sm font-semibold text-gray-500 px-2">
                            <span class="w-2"><i class="text-primary-700 fa-solid fa-user pr-1"></i></span>
                            <span>{{ item.instructor.first_name }} {{ item.instructor.last_name }}</span>
                        </div>
                    {% endif %}
                </div>

                <div class="pt-2 text-sm text-primary-700 px-2 text-right items-end">
                    {{ 'Meer info'|_ }}
                    <i class="fa-regular fa-chevron-right fa-sm translate-y-px pl-1"></i>
                </div>

            </div>
        </a>
    </div>
{% endif %}
