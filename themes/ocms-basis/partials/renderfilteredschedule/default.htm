{% put scripts %}
<script>
    jQuery(function(){
        $('.day-title').on('click', function(){
            if ( window.outerWidth < 1024) {
                $(this).closest('.day-wrap').find('.lessons-wrap').slideToggle();
                $(this).closest('.day-wrap').find('.day-title-icon').toggleClass('rotate-180');
            }
        });

        // $(window).on('resize', function(){
        //     if ( window.outerWidth > 1024) {
        //         $('.lessons-wrap').slideDown();
        //     } else {
        //         $('.lessons-wrap').slideUp();
        //     }
        // });
    });
</script>
{% endput %}

{% set locations = __SELF__.locations %}
{% if box.locations %}
    {% set locations = __SELF__.locations.whereIn('id', box.locations) %}
{% endif %}

<div class=""
    x-data="{
        mode: 'weekdays',
        locations: [],
        categories: [],
    }"
>
    <div class="md:flex md:flex-wrap md:gap-x-8 md:gap-y-4 md:justify-between md:items-center">

        <div>
            <ul class="hidden md:flex justify-center">
                <li>
                    <button type="button" @click="mode = 'weekdays'" :class="{ 'bg-primary-500 text-white': mode === 'weekdays', 'bg-gray-100 text-gray-800': mode !== 'weekdays' }" class="hover:bg-primary-600 hover:text-white mx-2 py-3.5 px-6 rounded-full text-xl font-bold leading-none">
                        Weekdagen
                    </button>
                </li>
                <li>
                    <button type="button" @click="mode = 'weekend'" :class="{ 'bg-primary-500 text-white': mode === 'weekend', 'bg-gray-100 text-gray-800': mode !== 'weekend' }" class="hover:bg-primary-600 hover:text-white mx-2 py-3.5 px-6 rounded-full text-xl font-bold leading-none">
                        Weekend
                    </button>
                </li>
            </ul>
        </div>

        <div class="space-y-3 md:space-y-0 md:flex md:flex-wrap md:gap-x-8 md:gap-y-4 md:justify-between md:items-center relative z-40">
           {% partial '@location-filter' %}
           {% partial '@category-filter' %}
        </div>

    </div>


    <div class="flex flex-wrap space-y-2 md:space-y-0 -mx-px mt-6" id="weekdays" x-show="mode === 'weekdays'">
        <div class="day-wrap w-full lg:w-1/5">

            {% partial '@day-title' title="Maandag" weekday="0" borderRadius="left" %}

            <div class="lessons-wrap {{ "now"|date('w') != 1 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:pr-1.5 lg:pb-0">

                {% if __SELF__.monday.sortBy('start')|length > 0 %}
                {% for item in __SELF__.monday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5">
            {% partial '@day-title' title="Dinsdag" weekday="1" %}

            <div class="lessons-wrap {{ "now"|date('w') != 2 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:px-1.5 lg:pb-0">
                {% if __SELF__.tuesday.sortBy('start')|length > 0 %}
                {% for item in __SELF__.tuesday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5">
            {% partial '@day-title' title="Woensdag" weekday="2" %}

            <div class="lessons-wrap {{ "now"|date('w') != 3 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:px-1.5 lg:pb-0">
                {% if __SELF__.wednesday|length > 0 %}
                {% for item in __SELF__.wednesday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5">
            {% partial '@day-title' title="Donderdag" weekday="3" %}

            <div class="lessons-wrap {{ "now"|date('w') != 4 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:px-1.5 lg:pb-0">
                {% if __SELF__.thursday|length > 0 %}
                {% for item in __SELF__.thursday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5">
            {% partial '@day-title' title="Vrijdag" weekday="4" borderRadius="right" %}

            <div class="lessons-wrap {{ "now"|date('w') != 5 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:px-1.5 lg:pb-0">
                {% if __SELF__.friday|length > 0 %}
                {% for item in __SELF__.friday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5 md:hidden">
            {% partial '@day-title' title="Zaterdag" weekday="0" %}

            <div class="lessons-wrap {{ "now"|date('w') != 6 ? 'hidden' }} md:block py-3 md:py-0 md:pb-3 md:pr-1.5 lg:pb-0">
                {% if __SELF__.saturday|length > 0 %}
                {% for item in __SELF__.saturday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="day-wrap w-full lg:w-1/5 md:hidden">
            {% partial '@day-title' title="Zondag" weekday="1" %}

            <div class="lessons-wrap hidden md:block py-3 md:py-0 md:pb-3 md:pl-1.5 lg:pb-0">
                {% if __SELF__.sunday|length > 0 %}
                {% for item in __SELF__.sunday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="hidden md:flex flex-wrap justify-center -mx-px mt-6" id="weekend" x-show="mode === 'weekend'" x-cloak>
        <div class="w-full lg:w-1/5">
            {% partial '@day-title' title="Zaterdag" weekday="0" borderRadius="left" %}
            <div class="lessons-wrap hidden md:block py-3 md:py-0 md:pb-3 md:px-0.5 lg:pb-0">
                {% if __SELF__.saturday|length > 0 %}
                {% for item in __SELF__.saturday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
        <div class="w-full lg:w-1/5">
            {% partial '@day-title' title="Zondag" weekday="1" borderRadius="right" %}
            <div class="lessons-wrap hidden md:block py-3 md:py-0 md:pb-3 md:px-0.5 lg:pb-0">
                {% if __SELF__.sunday|length > 0 %}
                {% for item in __SELF__.sunday.sortBy('start') %}
                {% partial '@lesson' item = item %}
                {% endfor %}
                {% else %}
                <div class="md:hidden py-4 text-lg text-red-600">{{ 'Geen (groeps)lessen op deze dag'|_ }}</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
