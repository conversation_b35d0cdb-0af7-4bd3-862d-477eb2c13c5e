<div>
    <label class="flex w-full group">
        <input type="checkbox" value="{{ loc.id }}" name="locations" x-model="locations" class="peer sr-only">
        <div class="flex-none flex items-center justify-center w-6 h-6 rounded border border-gray-300 bg-transparent peer-checked:border-green-500 peer-checked:bg-green-100 transition group-hover:border-gray-400 peer-checked:group-hover:border-green-600">
            <i class="fa-solid fa-check text-green-600" x-show="locations.includes('{{ loc.id }}')"></i>
        </div>
        <div class="font-medium text-gray-800 ml-3">
            {{ loc.title }}
        </div>
    </label>
</div>
