{% if borderRadius == 'left' %}
    {% if this.theme.design.border_radius == 'rounded-2xl' %}
        {% set imgRadius = 'rounded-2xl lg:rounded-none lg:rounded-l-2xl' %}
    {% elseif this.theme.design.border_radius == 'rounded-lg' %}
        {% set imgRadius = 'rounded-lg lg:rounded-none lg:rounded-l-lg' %}
    {% elseif this.theme.design.border_radius == 'rounded' %}
        {% set imgRadius = 'rounded lg:rounded-none lg:rounded-l' %}
    {% else %}
        {% set imgRadius = 'rounded-none' %}
    {% endif %}

{% elseif borderRadius == 'right' %}
    {% if this.theme.design.border_radius == 'rounded-2xl' %}
        {% set imgRadius = 'rounded-2xl lg:rounded-none lg:rounded-r-2xl' %}
    {% elseif this.theme.design.border_radius == 'rounded-lg' %}
        {% set imgRadius = 'rounded-lg lg:rounded-none lg:rounded-r-lg' %}
    {% elseif this.theme.design.border_radius == 'rounded' %}
        {% set imgRadius = 'rounded lg:rounded-none lg:rounded-r' %}
    {% else %}
        {% set imgRadius = 'rounded-none' %}
    {% endif %}
{% else %}
    {% if this.theme.design.border_radius == 'rounded-2xl' %}
        {% set imgRadius = 'rounded-2xl lg:rounded-none' %}
    {% elseif this.theme.design.border_radius == 'rounded-lg' %}
        {% set imgRadius = 'rounded-lg lg:rounded-none' %}
    {% elseif this.theme.design.border_radius == 'rounded' %}
        {% set imgRadius = 'rounded lg:rounded-none' %}
    {% else %}
        {% set imgRadius = 'rounded-none' %}
    {% endif %}
{% endif  %}

<div class="day-title py-5 px-3 lg:sticky lg:top-2 z-20 bg-primary-900 {{ imgRadius }} text-white text-xl font-bold leading-none flex md:block items-center">
    <div class="w-2/5 md:w-auto text-left md:text-center">
        {{ title }}
    </div>
    <div class="ml-auto md:hidden">
        <i class="day-title-icon fa-sharp fa-solid fa-circle-chevron-down"></i>
    </div>
</div>
