 {% if locations|length > 1 %}
    <div class="relative" x-data="openClose" @click.away="close" x-on:keydown.escape.prevent.stop="close">
        <button
            type="button"
            class="w-full md:w-auto py-3.5 px-6 hover:bg-gray-50 transition text-gray-800 text-xl font-bold inline-flex space-x-2 items-center rounded-full justify-between md:justify-normal"
            @click="toggle"
            :class="{ 'shadow md:shadow-none bg-white': open, 'bg-gray-100': !open }"
        >
            <span>Filter op locatie</span>
            <div class="hidden md:block">
                <span><i class="fa-regular fa-chevron-down"></i></span>
            </div>
            <div class="md:hidden">
                <span><i class="fa-regular fa-chevron-down" x-show="!(open)"></i></span>
                <span><i class="fa-regular fa-chevron-up" x-show="open"></i></span>
            </div>
        </button>
        <div class="px-5 md:hidden" x-cloak x-show="open" x-collapse  aria-hidden="true" :aria-hidden="!open">
            <div class="p-4 rounded-b-2xl bg-white">
                <div class="space-y-2">
                    {% for loc in locations %}
                        {% partial '@location-filter-item' %}
                    {% endfor %}
                </div>
            </div>
        </div>
        <div class="hidden md:block absolute inset-x-0" x-cloak x-show="open" aria-hidden="true" :aria-hidden="open">
            <div class="mt-2 p-4 rounded-2xl bg-white shadow-sm border border-gray-300">
                <div class="space-y-2">
                    {% for loc in locations %}
                        {% partial '@location-filter-item' %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
{% endif %}
