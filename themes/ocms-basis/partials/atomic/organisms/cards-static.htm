<div>
    <div class="space-y-8 flex flex-col md:space-y-0 md:flex-row md:flex-wrap justify-center -mx-4 {{ box.card_grid == '2' ? 'justify-center gap-y-4' : 'gap-y-8' }}">
        {% if box.card_grid == '2' %}

            {% if variable == "instructors" %}
                {% for row in __SELF__.instructors|batch(2) %}
                    {% for item in row %}
                        <div class="w-full md:w-1/2 {{ cols }} px-4">
                                {% partial 'atomic/molecules/cards/card-dans-instructors' item=item %}

                        </div>
                    {% endfor %}
                    <div class="w-full"></div>
                {% endfor %}

            {% elseif variable == "courses" %}

                {% for row in __SELF__.courses|batch(2) %}
                    {% for item in row %}
                        <div class="w-full md:w-1/2 {{ cols }} px-4">
                            {% partial 'atomic/molecules/cards/card-dans-courses' item=item %}

                        </div>
                    {% endfor %}
                    <div class="w-full"></div>
                {% endfor %}

            {% elseif variable == "locations" %}

                {% for row in __SELF__.locations|batch(2) %}
                    {% for item in row %}
                        <div class="w-full md:w-1/2 {{ cols }} px-4">
                            {% partial 'atomic/molecules/cards/card-dans-locations' item=item %}

                        </div>
                    {% endfor %}
                    <div class="w-full"></div>
                {% endfor %}

            {% endif %}

        {% else %}

            {% if variable == "instructors" %}

                {% for item in __SELF__.instructors %}

                    <div class="w-full md:w-1/2 {{ cols }} px-4">

                            {% partial 'atomic/molecules/cards/card-dans-instructors' item=item %}

                    </div>

                {% endfor %}

            {% elseif variable == "courses" %}

                {% for item in __SELF__.courses %}

                    <div class="w-full md:w-1/2 {{ cols }} px-4">
                        {% partial 'atomic/molecules/cards/card-dans-courses' item=item %}
                    </div>

                {% endfor %}

            {% elseif variable == "locations" %}

                {% for item in __SELF__.locations %}

                    <div class="w-full md:w-1/2 {{ cols }} px-4">
                        {% partial 'atomic/molecules/cards/card-dans-locations' item=item %}
                    </div>

                {% endfor %}

            {% endif %}

        {% endif %}
    </div>
</div>
