{% put styles %}
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
{% endput %}

{% put scripts %}
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script>
        $('.card-slider-1').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            infinite: true,
            dots: false,
            autoplay: true,
            autoplaySpeed: 5000,
            touchMove: false,
            prevArrow: '.card-slider-1-prev',
            nextArrow: '.card-slider-1-next',
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });
    </script>
{% endput %}

<div class="relative max-w-6xl mx-auto">

    <div class="grid grid-cols-2 gap-4 md:grid-cols-12 md:gap-8">

        <div class="h-full col-span-2 md:col-span-10">
            <div class="card-slider-1">

                {% if variable == "instructors" %}

                    {% for item in __SELF__.instructors %}

                        <div class="flex items-center justify-center h-full overflow-hidden px-4">
                            {% partial 'atomic/molecules/cards/card-dans-instructors' item=item class="" %}
                        </div>

                    {% endfor %}

                {% elseif variable == "courses" %}

                    {% for item in __SELF__.courses %}

                        <div class="flex items-center justify-center h-full overflow-hidden px-4">
                            {% partial 'atomic/molecules/cards/card-dans-courses' item=item class="" %}
                        </div>

                    {% endfor %}

                {% elseif variable == "locations" %}

                    {% for item in __SELF__.locations %}

                        <div class="flex items-center justify-center h-full overflow-hidden px-4">
                            {% partial 'atomic/molecules/cards/card-dans-locations' item=item class="" %}
                        </div>

                    {% endfor %}

                {% endif %}

            </div>
        </div>
        {# Prev button #}
        <div class="text-2xl text-gray-800 dark:md:text-gray-200 md:order-first flex items-center justify-center">
            <button type="button" class="card-slider-1-prev opacity-80 hover:opacity-100 transition">
                <i class="fa-solid fa-chevron-left"></i>
            </button>
        </div>

        {# Next button #}
        <div class="text-2xl text-gray-800 dark:md:text-gray-200 md:order-last flex items-center justify-center">
            <button type="button" class="card-slider-1-next opacity-80 hover:opacity-100 transition">
                <i class="fa-solid fa-chevron-right"></i>
            </button>
        </div>


    </div>
</div>
