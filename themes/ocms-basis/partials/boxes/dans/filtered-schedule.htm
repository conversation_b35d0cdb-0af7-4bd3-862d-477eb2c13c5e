<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="filtered-schedule"
    data-category="dans"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="relative container space-y-8 lg:space-y-12 z-10">
        <div class="space-y-2">
            {% partial 'atomic/molecules/content-heading-centered' box=box %}
        </div>

        <div>
            {% set instructors = box.instructors|json_encode %}
            {% set groups = box.groups|json_encode %}
            {% set locations = box.locations|json_encode %}
            {% ajaxPartial 'site/dynamic-schedule' instructors = instructors groups = groups locations = locations %}
        </div>
    </div>
    <div class="absolute inset-x-0 bottom-0">
        {% partial 'atomic/atoms/theme/wave-bottom' class="translate-y-[1px]" %}
        <div class="bg-white h-32 lg:h-40 xl:h-32 2xl:h-24"></div>
    </div>
</section>
