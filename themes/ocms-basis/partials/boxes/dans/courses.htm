{% set cols = "lg:w-1/3" %}
{% if box.card_grid == '4' %}
    {% set cols = "lg:w-1/3 xl:w-1/4" %}
{% endif %}

<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="courses"
    data-category="dans"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-8 lg:space-y-12">
        <div class="space-y-2">
            {% partial 'atomic/molecules/content-heading-centered' box=box %}
        </div>

        {% set groups = box.groups|json_encode %}
        {% set instructors = box.instructors|json_encode %}
        {% set locations = box.locations|json_encode %}
        {% ajaxPartial 'site/dynamic-courses' instructors=instructors groups=groups locations=locations %}
    </div>

</section>
